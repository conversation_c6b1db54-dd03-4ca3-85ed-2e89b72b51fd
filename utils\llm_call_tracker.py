"""
Simple LLM Token Tracker

Streamlined utility for tracking LLM calls with automatic token counting and cost calculation.
Just pass the model name and call data - everything else is handled automatically.
"""

from datetime import datetime
from typing import Dict, Any, Optional


# Simple pricing table - easy to update
MODEL_PRICING = {
    # OpenAI models (per 1K tokens)
    "gpt-4o-mini": {"input": 0.000150, "output": 0.000600},
    "gpt-4o": {"input": 0.005000, "output": 0.015000},
    "gpt-4": {"input": 0.030000, "output": 0.060000},
    "gpt-3.5-turbo": {"input": 0.001500, "output": 0.002000},
    # Google models (per 1K tokens)
    "gemini-1.5-flash": {"input": 0.000075, "output": 0.000300},
    "gemini-1.5-pro": {"input": 0.003500, "output": 0.010500},
    # Audio models
    "whisper-1": {"audio": 0.006000},  # per minute
    "tts-1": {"characters": 0.000015},  # per character
    # ElevenLabs (per character)
    "elevenlabs": {"characters": 0.000180},
}


def estimate_tokens(text: str) -> int:
    """Simple token estimation: ~4 characters per token"""
    return max(1, len(text) // 4)


def calculate_cost(model: str, input_tokens: int, output_tokens: int) -> float:
    """Calculate cost based on model pricing"""
    pricing = MODEL_PRICING.get(model.lower(), {"input": 0, "output": 0})

    input_cost = (input_tokens / 1000) * pricing.get("input", 0)
    output_cost = (output_tokens / 1000) * pricing.get("output", 0)

    return input_cost + output_cost


async def track_llm_call(memory_manager, model: str, input_text: str, output_text: str, context: str = "llm"):
    """
    Simple function to track any LLM call with automatic token counting and cost calculation.

    Args:
        memory_manager: MemoryManager instance
        model: Model name (e.g., "gpt-4o-mini", "gemini-1.5-flash")
        input_text: Text sent to the model
        output_text: Text received from the model
        context: Context description (e.g., "intent", "emotion", "response")
    """
    try:
        # Estimate tokens
        input_tokens = estimate_tokens(input_text)
        output_tokens = estimate_tokens(output_text)

        # Calculate cost
        cost = calculate_cost(model, input_tokens, output_tokens)

        # Create simple tracking data
        call_data = {
            "model": model,
            "context": context,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "cost_usd": cost,
            "timestamp": datetime.now().isoformat()
        }

        # Store in contextual memory for aggregation
        existing_calls = await memory_manager.contextual.get("llm_calls") or []
        existing_calls.append(call_data)

        # Keep only last 20 calls to prevent bloat
        if len(existing_calls) > 20:
            existing_calls = existing_calls[-20:]

        await memory_manager.contextual.set("llm_calls", existing_calls)

    except Exception as e:
        # Silent fail - don't break the main operation
        print(f"[Token Tracker] Error: {e}")


# Convenience functions for specific providers
async def track_openai_call(memory_manager, model: str, input_text: str, response, context: str = "llm"):
    """Track OpenAI API call"""
    try:
        output_text = response.choices[0].message.content.strip()
        await track_llm_call(memory_manager, model, input_text, output_text, context)
    except:
        pass  # Silent fail


async def track_gemini_call(memory_manager, model: str, input_text: str, response, context: str = "llm"):
    """Track Google Gemini API call"""
    try:
        output_text = response.text
        await track_llm_call(memory_manager, model, input_text, output_text, context)
    except:
        pass  # Silent fail


async def track_audio_call(memory_manager, model: str, duration_sec: float, output_text: str, context: str = "audio"):
    """Track audio model call (STT/TTS)"""
    try:
        # For audio, use duration for cost calculation
        if "whisper" in model.lower():
            cost = (duration_sec / 60) * MODEL_PRICING.get("whisper-1", {}).get("audio", 0)
            tokens = int(duration_sec * 2)  # Rough estimate
        else:  # TTS
            cost = len(output_text) * MODEL_PRICING.get("tts-1", {}).get("characters", 0)
            tokens = len(output_text)

        call_data = {
            "model": model,
            "context": context,
            "input_tokens": 0,
            "output_tokens": tokens,
            "cost_usd": cost,
            "timestamp": datetime.now().isoformat()
        }

        existing_calls = await memory_manager.contextual.get("llm_calls") or []
        existing_calls.append(call_data)

        if len(existing_calls) > 20:
            existing_calls = existing_calls[-20:]

        await memory_manager.contextual.set("llm_calls", existing_calls)

    except:
        pass  # Silent fail
