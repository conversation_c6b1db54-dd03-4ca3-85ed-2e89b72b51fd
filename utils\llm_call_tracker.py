"""
Universal LLM Token Tracker

One simple function to track any LLM call - just pass the model name and response data.
Automatically detects provider and handles token counting and cost calculation.
"""

from datetime import datetime
from typing import Dict, Any, Optional, Union


# Simple pricing table - easy to update
MODEL_PRICING = {
    # OpenAI models (per 1K tokens)
    "gpt-4o-mini": {"input": 0.000150, "output": 0.000600, "provider": "openai"},
    "gpt-4o": {"input": 0.005000, "output": 0.015000, "provider": "openai"},
    "gpt-4": {"input": 0.030000, "output": 0.060000, "provider": "openai"},
    "gpt-3.5-turbo": {"input": 0.001500, "output": 0.002000, "provider": "openai"},
    # Google models (per 1K tokens)
    "gemini-1.5-flash": {"input": 0.000075, "output": 0.000300, "provider": "google"},
    "gemini-1.5-pro": {"input": 0.003500, "output": 0.010500, "provider": "google"},
    # Audio models
    "whisper-1": {"audio": 0.006000, "provider": "openai"},  # per minute
    "tts-1": {"characters": 0.000015, "provider": "openai"},  # per character
    # ElevenLabs (per character)
    "elevenlabs": {"characters": 0.000180, "provider": "elevenlabs"},
}


def estimate_tokens(text: str) -> int:
    """Simple token estimation: ~4 characters per token"""
    return max(1, len(text) // 4)


def extract_response_text(response, model: str = "") -> str:
    """Extract text from different response formats"""
    try:
        # OpenAI response format
        if hasattr(response, 'choices') and response.choices:
            return response.choices[0].message.content.strip()

        # Gemini response format
        if hasattr(response, 'text'):
            return response.text.strip()

        # If it's already a string
        if isinstance(response, str):
            return response.strip()

        # Fallback
        return str(response)

    except:
        return ""


def calculate_cost(model: str, input_tokens: int, output_tokens: int, duration_sec: float = 0) -> float:
    """Calculate cost based on model pricing"""
    pricing = MODEL_PRICING.get(model.lower(), {})

    # Audio models (per minute or per character)
    if "whisper" in model.lower():
        return (duration_sec / 60) * pricing.get("audio", 0)
    elif "tts" in model.lower():
        return output_tokens * pricing.get("characters", 0)  # output_tokens = character count for TTS

    # Text models (per 1K tokens)
    input_cost = (input_tokens / 1000) * pricing.get("input", 0)
    output_cost = (output_tokens / 1000) * pricing.get("output", 0)

    return input_cost + output_cost


async def track_llm_call(memory_manager, model: str, input_text: str = "", response_or_output: Any = None, context: str = "llm", duration_sec: float = 0):
    """
    Universal function to track ANY LLM call with automatic token counting and cost calculation.

    Args:
        memory_manager: MemoryManager instance
        model: Model name (e.g., "gpt-4o-mini", "gemini-1.5-flash", "whisper-1", "tts-1")
        input_text: Text sent to the model (empty for TTS)
        response_or_output: Response object OR output text string
        context: Context description (e.g., "intent", "emotion", "decision", "stt", "tts")
        duration_sec: Audio duration for STT/TTS models (optional)

    Examples:
        # OpenAI text model
        await track_llm_call(memory_manager, "gpt-4o-mini", prompt, response, "intent")

        # Gemini model
        await track_llm_call(memory_manager, "gemini-1.5-flash", prompt, response, "decision")

        # Whisper STT
        await track_llm_call(memory_manager, "whisper-1", "", transcribed_text, "stt", duration_sec=30.0)

        # OpenAI TTS
        await track_llm_call(memory_manager, "tts-1", text_to_speak, "", "tts")
    """
    try:
        # Extract output text from response
        if isinstance(response_or_output, str):
            output_text = response_or_output
        else:
            output_text = extract_response_text(response_or_output, model)

        # Calculate tokens
        input_tokens = estimate_tokens(input_text) if input_text else 0

        # For TTS, output_tokens = character count; for others, estimate from text
        if "tts" in model.lower():
            output_tokens = len(input_text)  # TTS uses input text length for pricing
        else:
            output_tokens = estimate_tokens(output_text) if output_text else 0

        # Calculate cost
        cost = calculate_cost(model, input_tokens, output_tokens, duration_sec)

        # Create tracking data
        call_data = {
            "model": model,
            "context": context,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "cost_usd": cost,
            "timestamp": datetime.now().isoformat()
        }

        # Store in contextual memory
        existing_calls = await memory_manager.contextual.get("llm_calls") or []
        existing_calls.append(call_data)

        # Keep only last 20 calls to prevent bloat
        if len(existing_calls) > 20:
            existing_calls = existing_calls[-20:]

        await memory_manager.contextual.set("llm_calls", existing_calls)

    except Exception:
        # Silent fail - don't break the main operation
        pass
