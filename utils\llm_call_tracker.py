"""
LLM Call Tracker Utility

This module provides utilities for agents to track LLM calls and store metadata
in contextual memory for token usage tracking.
"""

from datetime import datetime
from typing import Dict, Any, Optional
import asyncio


class LLMCallTracker:
    """
    Utility class to help agents track LLM calls and store metadata
    for comprehensive token usage tracking.
    """
    
    def __init__(self, memory_manager):
        """
        Initialize the LLM call tracker.
        
        Args:
            memory_manager: MemoryManager instance for storing call metadata
        """
        self.memory_manager = memory_manager
    
    async def track_openai_call(self, model: str, input_text: str, response, context: str = "general"):
        """
        Track an OpenAI API call and store metadata in contextual memory.
        
        Args:
            model: Model name (e.g., "gpt-4o-mini")
            input_text: Input text sent to the model
            response: OpenAI response object
            context: Context description (e.g., "intent_classification", "emotion_detection")
        """
        try:
            output_text = response.choices[0].message.content.strip()
            
            call_metadata = {
                "timestamp": datetime.now().isoformat(),
                "model": model,
                "provider": "openai",
                "context": context,
                "input_text": input_text,
                "output_text": output_text,
                "input_length": len(input_text),
                "output_length": len(output_text)
            }
            
            # Store in contextual memory
            await self._store_call_metadata(call_metadata)
            
        except Exception as e:
            # Log error but don't fail the main operation
            print(f"[LLM Tracker] Failed to track OpenAI call: {e}")
    
    async def track_gemini_call(self, model: str, input_text: str, response, context: str = "general"):
        """
        Track a Google Gemini API call and store metadata in contextual memory.
        
        Args:
            model: Model name (e.g., "gemini-1.5-flash")
            input_text: Input text sent to the model
            response: Gemini response object
            context: Context description
        """
        try:
            output_text = response.text
            
            call_metadata = {
                "timestamp": datetime.now().isoformat(),
                "model": model,
                "provider": "google",
                "context": context,
                "input_text": input_text,
                "output_text": output_text,
                "input_length": len(input_text),
                "output_length": len(output_text)
            }
            
            # Store in contextual memory
            await self._store_call_metadata(call_metadata)
            
        except Exception as e:
            # Log error but don't fail the main operation
            print(f"[LLM Tracker] Failed to track Gemini call: {e}")
    
    async def track_audio_call(self, model: str, audio_duration_sec: float, output_text: str, context: str = "stt"):
        """
        Track an audio model call (STT/TTS) and store metadata.
        
        Args:
            model: Model name (e.g., "whisper-1", "tts-1")
            audio_duration_sec: Duration of audio in seconds
            output_text: Transcribed or synthesized text
            context: Context description (e.g., "stt", "tts")
        """
        try:
            call_metadata = {
                "timestamp": datetime.now().isoformat(),
                "model": model,
                "provider": "openai",
                "context": context,
                "audio_duration_sec": audio_duration_sec,
                "output_text": output_text,
                "output_length": len(output_text)
            }
            
            # Store in contextual memory
            await self._store_call_metadata(call_metadata)
            
        except Exception as e:
            # Log error but don't fail the main operation
            print(f"[LLM Tracker] Failed to track audio call: {e}")
    
    async def _store_call_metadata(self, call_metadata: Dict[str, Any]):
        """
        Store call metadata in contextual memory for later aggregation.
        
        Args:
            call_metadata: Dictionary containing call information
        """
        try:
            # Get existing LLM calls list
            existing_calls = await self.memory_manager.contextual.get("recent_llm_calls") or []
            
            # Add new call
            existing_calls.append(call_metadata)
            
            # Keep only last 50 calls to prevent memory bloat
            if len(existing_calls) > 50:
                existing_calls = existing_calls[-50:]
            
            # Store back in contextual memory
            await self.memory_manager.contextual.set("recent_llm_calls", existing_calls)
            
            # Also store as the most recent call for immediate access
            await self.memory_manager.contextual.set("last_llm_call", call_metadata)
            
        except Exception as e:
            print(f"[LLM Tracker] Failed to store call metadata: {e}")


# Convenience functions for easy integration
async def track_openai_call(memory_manager, model: str, input_text: str, response, context: str = "general"):
    """Convenience function to track OpenAI calls."""
    tracker = LLMCallTracker(memory_manager)
    await tracker.track_openai_call(model, input_text, response, context)

async def track_gemini_call(memory_manager, model: str, input_text: str, response, context: str = "general"):
    """Convenience function to track Gemini calls."""
    tracker = LLMCallTracker(memory_manager)
    await tracker.track_gemini_call(model, input_text, response, context)

async def track_audio_call(memory_manager, model: str, audio_duration_sec: float, output_text: str, context: str = "stt"):
    """Convenience function to track audio calls."""
    tracker = LLMCallTracker(memory_manager)
    await tracker.track_audio_call(model, audio_duration_sec, output_text, context)
