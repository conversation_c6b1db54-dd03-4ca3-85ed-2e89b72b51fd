"""
Example: Integrating Token Tracking into Existing Agents

This example shows how to modify existing agent methods to include
LLM token usage tracking with minimal code changes.
"""

from utils.llm_call_tracker import track_openai_call, track_gemini_call
from datetime import datetime


class ExamplePreprocessingAgent:
    """
    Example showing how to integrate token tracking into the PreprocessingAgent
    """
    
    def __init__(self, session_id=None, state_id=None):
        # ... existing initialization code ...
        self.memory_manager = None  # Initialized elsewhere
        self.openai_client = None   # Initialized elsewhere
    
    # BEFORE: Original method without token tracking
    async def classify_intent_original(self, text: str) -> str:
        prompt = (
            "Given the following user message, identify the user's intent as a single word or short phrase "
            "(e.g., inquire_balance, loan_inquiry, greeting, complaint, unknown). "
            "If the intent is unclear, respond with 'unknown'.\n"
            f"Message: {text}\nIntent:"
        )
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=5,
                temperature=0.2
            )
            intent = response.choices[0].message.content.strip().lower()
            return intent
        except Exception as e:
            self.logger.warning(f"Intent classification failed: {e}")
            return "unknown"
    
    # AFTER: Enhanced method with token tracking
    async def classify_intent_with_tracking(self, text: str) -> str:
        prompt = (
            "Given the following user message, identify the user's intent as a single word or short phrase "
            "(e.g., inquire_balance, loan_inquiry, greeting, complaint, unknown). "
            "If the intent is unclear, respond with 'unknown'.\n"
            f"Message: {text}\nIntent:"
        )
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=5,
                temperature=0.2
            )
            intent = response.choices[0].message.content.strip().lower()
            
            # ADD THIS: Track the LLM call for token usage
            await track_openai_call(
                memory_manager=self.memory_manager,
                model="gpt-4o-mini",
                input_text=prompt,
                response=response,
                context="intent_classification"
            )
            
            return intent
        except Exception as e:
            self.logger.warning(f"Intent classification failed: {e}")
            return "unknown"


class ExampleOrchestratorAgent:
    """
    Example showing how to integrate token tracking for Gemini calls
    """
    
    def __init__(self):
        self.memory_manager = None  # Initialized elsewhere
    
    # BEFORE: Original Gemini call without tracking
    async def get_gemini_decision_original(self, user_query, agent_responses, agent_confidence, current_state, workflow):
        import google.generativeai as genai
        import asyncio
        
        prompt = f"""
        User query: {user_query}
        Agent responses: {agent_responses}
        Agent confidence: {agent_confidence}
        Current state: {current_state}
        Workflow: {workflow}
        Should the system proceed to the next state or redo the current state? Respond with 'proceed' or 'redo' and a short explanation.
        """
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, lambda: model.generate_content(prompt))
            text = response.text.lower()
            
            if "proceed" in text:
                return "proceed"
            elif "redo" in text:
                return "redo"
            else:
                raise ValueError("Gemini did not return a valid decision.")
        except Exception as e:
            raise
    
    # AFTER: Enhanced method with token tracking
    async def get_gemini_decision_with_tracking(self, user_query, agent_responses, agent_confidence, current_state, workflow):
        import google.generativeai as genai
        import asyncio
        
        prompt = f"""
        User query: {user_query}
        Agent responses: {agent_responses}
        Agent confidence: {agent_confidence}
        Current state: {current_state}
        Workflow: {workflow}
        Should the system proceed to the next state or redo the current state? Respond with 'proceed' or 'redo' and a short explanation.
        """
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, lambda: model.generate_content(prompt))
            text = response.text.lower()
            
            # ADD THIS: Track the Gemini call for token usage
            await track_gemini_call(
                memory_manager=self.memory_manager,
                model="gemini-1.5-flash",
                input_text=prompt,
                response=response,
                context="orchestrator_decision"
            )
            
            if "proceed" in text:
                return "proceed"
            elif "redo" in text:
                return "redo"
            else:
                raise ValueError("Gemini did not return a valid decision.")
        except Exception as e:
            raise


class ExampleSTTAgent:
    """
    Example showing how to integrate token tracking for audio models
    """
    
    def __init__(self):
        self.memory_manager = None  # Initialized elsewhere
        self.client = None          # OpenAI client
    
    # BEFORE: Original STT method without tracking
    async def transcribe_original(self, audio_path: str, language: str = None, response_format: str = "text"):
        import asyncio
        
        def sync_transcribe():
            with open(audio_path, "rb") as audio_file:
                return self.client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    language=language,
                    response_format=response_format
                )
        
        transcription = await asyncio.to_thread(sync_transcribe)
        if hasattr(transcription, "text"):
            text = transcription.text
        else:
            text = transcription
        
        return text
    
    # AFTER: Enhanced method with token tracking
    async def transcribe_with_tracking(self, audio_path: str, language: str = None, response_format: str = "text"):
        import asyncio
        import os
        from utils.llm_call_tracker import track_audio_call
        
        # Get audio duration for cost calculation
        # Note: In production, you'd use a proper audio library like librosa or pydub
        audio_duration_sec = 30.0  # Placeholder - replace with actual duration calculation
        
        def sync_transcribe():
            with open(audio_path, "rb") as audio_file:
                return self.client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    language=language,
                    response_format=response_format
                )
        
        transcription = await asyncio.to_thread(sync_transcribe)
        if hasattr(transcription, "text"):
            text = transcription.text
        else:
            text = transcription
        
        # ADD THIS: Track the audio call for token usage
        await track_audio_call(
            memory_manager=self.memory_manager,
            model="whisper-1",
            audio_duration_sec=audio_duration_sec,
            output_text=text,
            context="stt"
        )
        
        return text


# Alternative approach: Manual metadata storage
class ExampleManualTracking:
    """
    Example showing manual metadata storage approach
    """
    
    async def store_llm_metadata_manually(self, model: str, provider: str, input_text: str, output_text: str, context: str):
        """
        Manually store LLM call metadata in contextual memory
        """
        call_metadata = {
            "timestamp": datetime.now().isoformat(),
            "model": model,
            "provider": provider,
            "context": context,
            "input_text": input_text,
            "output_text": output_text,
            "input_length": len(input_text),
            "output_length": len(output_text)
        }
        
        # Get existing calls
        recent_calls = await self.memory_manager.contextual.get("recent_llm_calls") or []
        recent_calls.append(call_metadata)
        
        # Keep only last 50 calls
        if len(recent_calls) > 50:
            recent_calls = recent_calls[-50:]
        
        # Store back
        await self.memory_manager.contextual.set("recent_llm_calls", recent_calls)
        await self.memory_manager.contextual.set("last_llm_call", call_metadata)


# Summary of Integration Steps:
"""
1. Import the tracking utilities:
   from utils.llm_call_tracker import track_openai_call, track_gemini_call, track_audio_call

2. After each LLM API call, add tracking:
   await track_openai_call(self.memory_manager, model, input_text, response, context)

3. The memory manager will automatically:
   - Extract metadata from contextual memory
   - Count tokens using appropriate methods
   - Calculate costs using current pricing
   - Aggregate session totals
   - Store in MongoDB

4. No other changes needed - the system is backward compatible!
"""
