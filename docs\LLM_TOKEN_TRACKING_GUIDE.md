# LLM Token Usage Tracking & Cost Estimation Guide

## Overview

The AI Voice Agent Platform now includes comprehensive LLM token usage tracking and cost estimation capabilities. This system automatically tracks tokens and costs across all supported AI models and providers.

## Features

### Multi-Provider Support
- **OpenAI Models**: GPT-4o-mini, GPT-4o, GPT-3.5-turbo, Whisper-1, TTS-1
- **Google Models**: Gemini-1.5-flash, Gemini-1.5-pro, Gemini-pro
- **ElevenLabs**: TTS character-based pricing
- **Future-Ready**: Extensible architecture for new providers

### Token Counting Methods
1. **tiktoken** (OpenAI models) - Most accurate
2. **transformers** (Google models) - Good approximation
3. **Character-based estimation** - Fallback method
4. **Audio duration estimation** - For speech models

### Cost Calculation
- Real-time cost estimation using current pricing tables
- Per-1K token pricing for text models
- Per-minute pricing for audio models
- Per-character pricing for TTS models

## Architecture

### Central Aggregation Point
All token tracking is centralized in `core/memory/memory_manager.py` within the `_save_dialog_log_internal()` method. This approach:
- Minimizes code changes across the platform
- Leverages existing metric calculation patterns
- Ensures consistent data collection
- Maintains backward compatibility

### Data Flow
```
LLM Call → Agent stores metadata in Redis → Memory Manager aggregates → MongoDB storage
```

## Implementation Details

### MongoDB Schema Updates
New fields added to `CallSession` collection:
```python
tokens_in: Optional[int] = None          # Total input tokens
tokens_out: Optional[int] = None         # Total output tokens  
estimated_cost_usd: Optional[float] = None  # Total estimated cost
```

### Memory Manager Integration
The `_save_dialog_log_internal()` method now:
1. Extracts LLM call metadata from contextual memory
2. Counts tokens using provider-specific methods
3. Calculates costs using current pricing tables
4. Aggregates session totals
5. Stores detailed token information in dialog logs
6. Passes metrics to MongoDB via existing save patterns

## Usage for Agent Developers

### Option 1: Using the LLM Call Tracker Utility

```python
from utils.llm_call_tracker import track_openai_call

# In your agent after making an LLM call
response = await self.openai_client.chat.completions.create(...)
await track_openai_call(
    memory_manager=self.memory_manager,
    model="gpt-4o-mini",
    input_text=prompt,
    response=response,
    context="intent_classification"
)
```

### Option 2: Manual Metadata Storage

```python
# Store LLM call metadata in contextual memory
call_metadata = {
    "timestamp": datetime.now().isoformat(),
    "model": "gpt-4o-mini",
    "provider": "openai",
    "context": "emotion_detection",
    "input_text": prompt,
    "output_text": response.choices[0].message.content,
}

# Add to recent calls list
recent_calls = await self.memory_manager.contextual.get("recent_llm_calls") or []
recent_calls.append(call_metadata)
await self.memory_manager.contextual.set("recent_llm_calls", recent_calls)
```

## Pricing Tables

### OpenAI Models (per 1K tokens)
- **gpt-4o-mini**: Input $0.000150, Output $0.000600
- **gpt-4o**: Input $0.005000, Output $0.015000
- **whisper-1**: $0.006000 per minute
- **tts-1**: $0.000015 per character

### Google Models (per 1K tokens)
- **gemini-1.5-flash**: Input $0.000075, Output $0.000300
- **gemini-1.5-pro**: Input $0.003500, Output $0.010500

### ElevenLabs (per character)
- **Standard**: $0.000180
- **Professional**: $0.000240
- **Premium**: $0.000300

## Error Handling

The system includes comprehensive error handling:
- **Missing Dependencies**: Falls back to character-based estimation
- **Unknown Models**: Logs warnings, sets cost to $0
- **Tokenization Failures**: Uses fallback methods
- **Missing Metadata**: Continues processing with empty values

## Backward Compatibility

- All new fields are Optional in MongoDB schema
- Existing sessions without token data continue to work
- System gracefully handles missing tokenization libraries
- No breaking changes to existing agent interfaces

## Monitoring & Debugging

### Dialog Log Files
Token information is automatically included in dialog logs:
```
Total Input Tokens: 1,247
Total Output Tokens: 892
Total Estimated Cost: $0.001834 USD
TTS Characters Used: 156

=== Token Usage Details ===
Turn 0 (user): gpt-4o-mini (openai) - In: 45, Out: 0, Cost: $0.000007, Method: tiktoken
Turn 1 (ai): gpt-4o-mini (openai) - In: 0, Out: 23, Cost: $0.000014, Method: tiktoken
```

### MongoDB Queries
Query sessions by cost:
```javascript
db.call_sessions.find({"estimated_cost_usd": {$gt: 0.01}})
```

Query high token usage:
```javascript
db.call_sessions.find({"tokens_in": {$gt: 1000}})
```

## Dependencies

Add to your requirements.txt:
```
tiktoken>=0.5.0          # For OpenAI token counting
transformers>=4.30.0     # For Google model tokenization
```

Both dependencies are optional - the system will use fallback methods if unavailable.

## Future Enhancements

The architecture supports easy addition of:
- New AI providers (Anthropic Claude, Cohere, etc.)
- Custom tokenization methods
- Advanced cost optimization features
- Real-time cost alerts
- Usage analytics and reporting
