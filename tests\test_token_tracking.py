"""
Test script for Simple LLM Token Tracking

This script tests the simplified token tracking functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from utils.llm_call_tracker import track_llm_call, estimate_tokens, calculate_cost, MODEL_PRICING
from core.memory.memory_manager import MemoryManager


async def test_simple_token_functions():
    """Test the simple token estimation and cost calculation"""

    print("=== Testing Simple Token Functions ===\n")

    sample_text = "Hello, how can I help you with your account balance today?"

    print("1. Testing token estimation:")
    tokens = estimate_tokens(sample_text)
    print(f"   Text: '{sample_text}'")
    print(f"   Estimated tokens: {tokens}")

    print("\n2. Testing cost calculation for different models:")
    test_models = ["gpt-4o-mini", "gemini-1.5-flash", "gpt-4o", "unknown-model"]

    for model in test_models:
        cost = calculate_cost(model, tokens, tokens)
        print(f"   {model}: ${cost:.6f}")

    print("\n3. Testing pricing table:")
    print("   Available models and pricing:")
    for model, pricing in MODEL_PRICING.items():
        print(f"   {model}: {pricing}")


async def test_llm_call_tracking():
    """Test the LLM call tracking functionality"""

    print("\n=== Testing LLM Call Tracking ===\n")

    # Create a memory manager instance
    memory_manager = MemoryManager("test_session_tracking", "test_user")

    # Test tracking a simple LLM call
    await track_llm_call(
        memory_manager=memory_manager,
        model="gpt-4o-mini",
        input_text="What is the weather today?",
        output_text="I don't have access to current weather data.",
        context="weather_query"
    )

    # Test tracking another call
    await track_llm_call(
        memory_manager=memory_manager,
        model="gemini-1.5-flash",
        input_text="Classify this intent: check balance",
        output_text="account_inquiry",
        context="intent_classification"
    )

    # Retrieve and verify the stored calls
    stored_calls = await memory_manager.contextual.get("llm_calls")
    print(f"Stored {len(stored_calls)} LLM calls:")

    for i, call in enumerate(stored_calls):
        print(f"   Call {i+1}: {call['model']} - {call['context']}")
        print(f"      Tokens: {call['input_tokens']} in, {call['output_tokens']} out")
        print(f"      Cost: ${call['cost_usd']:.6f}")


async def test_pricing_lookup():
    """Test pricing lookup functionality"""

    print("\n=== Testing Pricing Lookup ===\n")

    test_cases = [
        ("gpt-4o-mini", 100, 50),
        ("gemini-1.5-flash", 200, 75),
        ("gpt-4o", 50, 25),
        ("unknown-model", 100, 100)
    ]

    for model, input_tokens, output_tokens in test_cases:
        cost = calculate_cost(model, input_tokens, output_tokens)
        print(f"{model}: {input_tokens} in + {output_tokens} out = ${cost:.6f}")





async def main():
    """Run all tests"""
    print("Simple LLM Token Tracking Test Suite")
    print("=" * 50)

    try:
        await test_simple_token_functions()
        await test_llm_call_tracking()
        await test_pricing_lookup()

        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        print("\nThe simplified token tracking system is working correctly.")
        print("No external dependencies required - uses simple character-based estimation.")

    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
