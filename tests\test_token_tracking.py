"""
Test script for LLM Token Tracking functionality

This script tests the token counting and cost calculation features
of the memory manager without requiring actual LLM API calls.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.memory.memory_manager import MemoryManager
from core.memory.token_tracker import TokenTracker


async def test_token_counting():
    """Test token counting methods with sample data"""

    # Create a token tracker instance
    token_tracker = TokenTracker("test_session_token_tracking")

    print("=== Testing Token Counting Methods ===\n")

    # Test OpenAI token counting
    sample_text = "Hello, how can I help you with your account balance today?"

    print("1. Testing OpenAI token counting:")
    try:
        result = token_tracker._count_tokens_openai(sample_text, "gpt-4o-mini", is_input=True)
        print(f"   Text: '{sample_text}'")
        print(f"   Tokens: {result['tokens']}")
        print(f"   Cost: ${result['cost_usd']:.6f}")
        print(f"   Method: {result['method']}")
    except Exception as e:
        print(f"   Error (expected if t<PERSON><PERSON><PERSON> not installed): {e}")

    print("\n2. Testing Google token counting:")
    try:
        result = token_tracker._count_tokens_google(sample_text, "gemini-1.5-flash", is_input=True)
        print(f"   Text: '{sample_text}'")
        print(f"   Tokens: {result['tokens']}")
        print(f"   Cost: ${result['cost_usd']:.6f}")
        print(f"   Method: {result['method']}")
    except Exception as e:
        print(f"   Error (expected if transformers not installed): {e}")

    print("\n3. Testing ElevenLabs character counting:")
    result = token_tracker._count_tokens_elevenlabs(sample_text, "standard")
    print(f"   Text: '{sample_text}'")
    print(f"   Characters: {result['tokens']}")
    print(f"   Cost: ${result['cost_usd']:.6f}")
    print(f"   Method: {result['method']}")

    print("\n4. Testing fallback token counting:")
    result = token_tracker._count_tokens_fallback(sample_text, "unknown-model", "unknown-provider", is_input=True)
    print(f"   Text: '{sample_text}'")
    print(f"   Estimated tokens: {result['tokens']}")
    print(f"   Cost: ${result['cost_usd']:.6f}")
    print(f"   Method: {result['method']}")

    print("\n5. Testing audio token estimation:")
    result = token_tracker.estimate_audio_tokens(30.0, "whisper-1")  # 30 seconds
    print(f"   Audio duration: 30 seconds")
    print(f"   Estimated tokens: {result['tokens']}")
    print(f"   Cost: ${result['cost_usd']:.6f}")
    print(f"   Method: {result['method']}")


async def test_pricing_tables():
    """Test pricing table functionality"""

    token_tracker = TokenTracker("test_session_pricing")

    print("\n=== Testing Pricing Tables ===\n")

    # Test various model pricing
    test_cases = [
        ("gpt-4o-mini", "openai"),
        ("gemini-1.5-flash", "google"),
        ("standard", "elevenlabs"),
        ("unknown-model", "unknown-provider")
    ]

    for model, provider in test_cases:
        pricing = token_tracker.get_model_pricing(model, provider)
        print(f"{provider}/{model}: {pricing}")


async def test_contextual_memory_integration():
    """Test storing and retrieving LLM call metadata"""
    
    memory_manager = MemoryManager("test_session_context", "test_user")
    
    print("\n=== Testing Contextual Memory Integration ===\n")
    
    # Simulate storing LLM call metadata
    sample_calls = [
        {
            "timestamp": "2025-07-14T10:00:00",
            "model": "gpt-4o-mini",
            "provider": "openai",
            "context": "intent_classification",
            "input_text": "What is my account balance?",
            "output_text": "inquire_balance",
            "input_length": 26,
            "output_length": 15
        },
        {
            "timestamp": "2025-07-14T10:00:05",
            "model": "gpt-4o-mini",
            "provider": "openai",
            "context": "emotion_detection",
            "input_text": "What is my account balance?",
            "output_text": "neutral",
            "input_length": 26,
            "output_length": 7
        }
    ]
    
    # Store in contextual memory
    await memory_manager.contextual.set("recent_llm_calls", sample_calls)
    
    # Retrieve and verify
    retrieved_calls = await memory_manager.contextual.get("recent_llm_calls")
    print(f"Stored {len(sample_calls)} LLM calls in contextual memory")
    print(f"Retrieved {len(retrieved_calls)} LLM calls from contextual memory")
    
    for i, call in enumerate(retrieved_calls):
        print(f"  Call {i+1}: {call['model']} ({call['context']}) - "
              f"In: {call['input_length']}, Out: {call['output_length']}")


async def test_conversation_processing():
    """Test conversation processing with token data"""
    
    memory_manager = MemoryManager("test_session_conversation", "test_user")
    
    print("\n=== Testing Conversation Processing ===\n")
    
    # Simulate a conversation with LLM metadata
    conversation = [
        {
            "role": "user",
            "text": "What is my account balance?",
            "timestamp": "2025-07-14T10:00:00"
        },
        {
            "role": "ai",
            "text": "Your current account balance is $2,547.83",
            "timestamp": "2025-07-14T10:00:05",
            "latencySTT": 150,
            "latencyProcessing": 300,
            "latencyTTS": 200,
            "total_latency": 650
        }
    ]
    
    # Store conversation and LLM calls
    await memory_manager.contextual.set("conversation", conversation)
    
    # Store LLM call metadata
    llm_calls = [
        {
            "timestamp": "2025-07-14T10:00:02",
            "model": "gpt-4o-mini",
            "provider": "openai",
            "context": "intent_classification",
            "input_text": "What is my account balance?",
            "output_text": "inquire_balance"
        },
        {
            "timestamp": "2025-07-14T10:00:03",
            "model": "gpt-4o-mini",
            "provider": "openai",
            "context": "response_generation",
            "input_text": "Generate response for balance inquiry",
            "output_text": "Your current account balance is $2,547.83"
        }
    ]
    
    await memory_manager.contextual.set("recent_llm_calls", llm_calls)
    
    print("Simulated conversation and LLM calls stored in contextual memory")
    print("Ready for token aggregation during dialog log processing")


async def main():
    """Run all tests"""
    print("LLM Token Tracking Test Suite")
    print("=" * 50)
    
    try:
        await test_token_counting()
        await test_pricing_tables()
        await test_contextual_memory_integration()
        await test_conversation_processing()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        print("\nNote: Some token counting methods may show errors if optional")
        print("dependencies (tiktoken, transformers) are not installed.")
        print("This is expected behavior - the system will use fallback methods.")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
