"""
Simple Agent Integration Examples

Shows how to easily add token tracking to any agent with minimal code changes.
Just import the tracker and add one line after each LLM call.
"""

from utils.llm_call_tracker import track_llm_call, track_openai_call, track_gemini_call


class SimplePreprocessingAgent:
    """Example showing minimal integration for preprocessing agent"""
    
    def __init__(self, memory_manager):
        self.memory_manager = memory_manager
        self.openai_client = None  # Initialized elsewhere
    
    async def classify_intent(self, text: str) -> str:
        """Intent classification with token tracking"""
        prompt = f"Classify the intent of this message: {text}\nIntent:"
        
        try:
            # Make the LLM call
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=5,
                temperature=0.2
            )
            
            intent = response.choices[0].message.content.strip().lower()
            
            # ADD THIS ONE LINE for token tracking
            await track_openai_call(self.memory_manager, "gpt-4o-mini", prompt, response, "intent")
            
            return intent
            
        except Exception as e:
            print(f"Intent classification failed: {e}")
            return "unknown"
    
    async def detect_emotion(self, text: str) -> str:
        """Emotion detection with token tracking"""
        prompt = f"What emotion is expressed in this message: {text}\nEmotion:"
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1,
                temperature=0.1
            )
            
            emotion = response.choices[0].message.content.strip().lower()
            
            # ADD THIS ONE LINE for token tracking
            await track_openai_call(self.memory_manager, "gpt-4o-mini", prompt, response, "emotion")
            
            return emotion
            
        except Exception as e:
            print(f"Emotion detection failed: {e}")
            return "neutral"


class SimpleOrchestratorAgent:
    """Example showing minimal integration for orchestrator agent"""
    
    def __init__(self, memory_manager):
        self.memory_manager = memory_manager
    
    async def make_decision(self, user_query: str, agent_responses: str) -> str:
        """Decision making with Gemini and token tracking"""
        import google.generativeai as genai
        import asyncio
        
        prompt = f"""
        User query: {user_query}
        Agent responses: {agent_responses}
        Should we proceed or redo? Respond with 'proceed' or 'redo'.
        """
        
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, lambda: model.generate_content(prompt))
            
            decision = response.text.lower()
            
            # ADD THIS ONE LINE for token tracking
            await track_gemini_call(self.memory_manager, "gemini-1.5-flash", prompt, response, "decision")
            
            return "proceed" if "proceed" in decision else "redo"
            
        except Exception as e:
            print(f"Decision making failed: {e}")
            return "redo"


class SimpleProcessingAgent:
    """Example showing minimal integration for processing agent"""
    
    def __init__(self, memory_manager):
        self.memory_manager = memory_manager
        self.openai_client = None  # Initialized elsewhere
    
    async def generate_response(self, user_query: str, context: str) -> str:
        """Response generation with token tracking"""
        prompt = f"""
        Context: {context}
        User Query: {user_query}
        Generate a helpful response:
        """
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=128,
                temperature=0.7
            )
            
            generated_response = response.choices[0].message.content.strip()
            
            # ADD THIS ONE LINE for token tracking
            await track_openai_call(self.memory_manager, "gpt-4o-mini", prompt, response, "response")
            
            return generated_response
            
        except Exception as e:
            print(f"Response generation failed: {e}")
            return "I apologize, but I'm having trouble processing your request right now."


class SimpleSTTAgent:
    """Example showing minimal integration for STT agent"""
    
    def __init__(self, memory_manager):
        self.memory_manager = memory_manager
        self.client = None  # OpenAI client
    
    async def transcribe(self, audio_path: str) -> str:
        """Speech-to-text with token tracking"""
        import asyncio
        from utils.llm_call_tracker import track_audio_call
        
        try:
            # Get audio duration (simplified - in production use proper audio library)
            audio_duration_sec = 30.0  # Placeholder
            
            def sync_transcribe():
                with open(audio_path, "rb") as audio_file:
                    return self.client.audio.transcriptions.create(
                        model="whisper-1",
                        file=audio_file,
                        response_format="text"
                    )
            
            transcription = await asyncio.to_thread(sync_transcribe)
            text = transcription if isinstance(transcription, str) else transcription.text
            
            # ADD THIS ONE LINE for token tracking
            await track_audio_call(self.memory_manager, "whisper-1", audio_duration_sec, text, "stt")
            
            return text
            
        except Exception as e:
            print(f"Transcription failed: {e}")
            return ""


# Alternative: Use the generic track_llm_call function for any model
class FlexibleAgent:
    """Example showing the most flexible approach"""
    
    def __init__(self, memory_manager):
        self.memory_manager = memory_manager
    
    async def call_any_model(self, model_name: str, input_text: str, output_text: str, context: str):
        """Generic LLM call tracking for any model"""
        
        # Just one simple call for any LLM interaction
        await track_llm_call(
            memory_manager=self.memory_manager,
            model=model_name,
            input_text=input_text,
            output_text=output_text,
            context=context
        )


# Summary of integration steps:
"""
1. Import the tracker:
   from utils.llm_call_tracker import track_openai_call, track_gemini_call, track_audio_call

2. After each LLM call, add ONE line:
   await track_openai_call(self.memory_manager, "gpt-4o-mini", prompt, response, "context")

3. That's it! The system automatically:
   - Estimates tokens (4 chars = 1 token)
   - Calculates costs using built-in pricing
   - Stores data in contextual memory
   - Aggregates totals in MongoDB
   - Includes details in dialog logs

No complex setup, no configuration, no additional dependencies required!
"""
