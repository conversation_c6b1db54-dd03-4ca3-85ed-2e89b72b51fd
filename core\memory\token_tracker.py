"""
LLM Token Usage Tracking and Cost Estimation Module

This module provides comprehensive token counting and cost calculation
for multiple AI providers and models.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from core.logging.logger_config import get_module_logger


class TokenTracker:
    """
    Centralized token tracking and cost calculation for LLM interactions.
    Supports multiple providers with fallback mechanisms.
    """
    
    def __init__(self, session_id: str = None):
        self.session_id = session_id
        self.logger = get_module_logger("TokenTracker", session_id=session_id)
        
        # Pricing table - easily updatable for new models/pricing changes
        self.pricing_table = {
            "openai": {
                "gpt-4o-mini": {"input": 0.000150, "output": 0.000600},  # per 1K tokens
                "gpt-4o": {"input": 0.005000, "output": 0.015000},
                "gpt-4": {"input": 0.030000, "output": 0.060000},
                "gpt-3.5-turbo": {"input": 0.001500, "output": 0.002000},
                "whisper-1": {"audio": 0.006000},  # per minute
                "tts-1": {"characters": 0.000015},  # per character
                "tts-1-hd": {"characters": 0.000030}
            },
            "google": {
                "gemini-1.5-flash": {"input": 0.000075, "output": 0.000300},  # per 1K tokens
                "gemini-1.5-pro": {"input": 0.003500, "output": 0.010500},
                "gemini-pro": {"input": 0.000500, "output": 0.001500}
            },
            "elevenlabs": {
                "standard": {"characters": 0.000180},  # per character (estimated)
                "professional": {"characters": 0.000240},
                "premium": {"characters": 0.000300}
            }
        }
    
    def get_model_pricing(self, model: str, provider: str) -> Dict[str, float]:
        """
        Get pricing information for any model/provider combination.
        Returns pricing per 1K tokens (input/output) or per unit for audio models.
        """
        provider_pricing = self.pricing_table.get(provider.lower(), {})
        model_pricing = provider_pricing.get(model.lower(), {})
        
        if not model_pricing:
            self.logger.warning(
                f"No pricing data found for model {model} from provider {provider}",
                action="get_model_pricing",
                input_data={"model": model, "provider": provider},
                layer="token_tracker"
            )
            return {"input": 0.0, "output": 0.0, "characters": 0.0, "audio": 0.0}
        
        return model_pricing
    
    def count_tokens_by_provider(self, text: str, model: str, provider: str, is_input: bool = True) -> Dict[str, Any]:
        """
        Count tokens for any provider/model combination.
        Returns {"tokens": int, "cost_usd": float, "method": str}
        """
        try:
            provider = provider.lower()
            model = model.lower()
            
            if provider == "openai":
                return self._count_tokens_openai(text, model, is_input)
            elif provider == "google":
                return self._count_tokens_google(text, model, is_input)
            elif provider == "elevenlabs":
                return self._count_tokens_elevenlabs(text, model)
            else:
                # Fallback to character-based estimation
                return self._count_tokens_fallback(text, model, provider, is_input)
                
        except Exception as e:
            self.logger.warning(
                f"Token counting failed for {provider}/{model}: {e}",
                action="count_tokens_by_provider",
                input_data={"text_length": len(text), "model": model, "provider": provider},
                layer="token_tracker"
            )
            return self._count_tokens_fallback(text, model, provider, is_input)
    
    def _count_tokens_openai(self, text: str, model: str, is_input: bool) -> Dict[str, Any]:
        """Count tokens for OpenAI models using tiktoken when available."""
        try:
            # Try to import tiktoken (optional dependency)
            import tiktoken
            
            # Map model names to tiktoken encodings
            model_encodings = {
                "gpt-4o-mini": "o200k_base",
                "gpt-4o": "o200k_base", 
                "gpt-4": "cl100k_base",
                "gpt-3.5-turbo": "cl100k_base"
            }
            
            encoding_name = model_encodings.get(model, "cl100k_base")
            encoding = tiktoken.get_encoding(encoding_name)
            tokens = len(encoding.encode(text))
            
            # Calculate cost
            pricing = self.get_model_pricing(model, "openai")
            cost_per_1k = pricing.get("input" if is_input else "output", 0.0)
            cost_usd = (tokens / 1000) * cost_per_1k
            
            return {
                "tokens": tokens,
                "cost_usd": cost_usd,
                "method": "tiktoken"
            }
            
        except ImportError:
            # Fallback to character-based estimation if tiktoken not available
            return self._count_tokens_fallback(text, model, "openai", is_input)
    
    def _count_tokens_google(self, text: str, model: str, is_input: bool) -> Dict[str, Any]:
        """Count tokens for Google models using transformers tokenizer when available."""
        try:
            # Try to use transformers tokenizer
            from transformers import AutoTokenizer
            
            # Use a generic tokenizer for estimation (Google models use SentencePiece)
            tokenizer = AutoTokenizer.from_pretrained("google/flan-t5-small")
            tokens = len(tokenizer.encode(text))
            
            # Calculate cost
            pricing = self.get_model_pricing(model, "google")
            cost_per_1k = pricing.get("input" if is_input else "output", 0.0)
            cost_usd = (tokens / 1000) * cost_per_1k
            
            return {
                "tokens": tokens,
                "cost_usd": cost_usd,
                "method": "transformers"
            }
            
        except (ImportError, Exception):
            # Fallback to character-based estimation
            return self._count_tokens_fallback(text, model, "google", is_input)
    
    def _count_tokens_elevenlabs(self, text: str, model: str) -> Dict[str, Any]:
        """Count characters for ElevenLabs TTS (character-based pricing)."""
        characters = len(text)
        
        # Calculate cost
        pricing = self.get_model_pricing(model, "elevenlabs")
        cost_per_char = pricing.get("characters", 0.000180)  # Default to standard pricing
        cost_usd = characters * cost_per_char
        
        return {
            "tokens": characters,  # For consistency, store as "tokens" 
            "cost_usd": cost_usd,
            "method": "character_count"
        }
    
    def _count_tokens_fallback(self, text: str, model: str, provider: str, is_input: bool) -> Dict[str, Any]:
        """Fallback token estimation using character/word-based approximation."""
        # Rough estimation: 1 token ≈ 4 characters for most models
        estimated_tokens = len(text) // 4
        
        # Calculate cost using fallback pricing
        pricing = self.get_model_pricing(model, provider)
        cost_per_1k = pricing.get("input" if is_input else "output", 0.0)
        cost_usd = (estimated_tokens / 1000) * cost_per_1k
        
        return {
            "tokens": estimated_tokens,
            "cost_usd": cost_usd,
            "method": "character_estimation"
        }
    
    def estimate_audio_tokens(self, audio_duration_sec: float, model: str = "whisper-1") -> Dict[str, Any]:
        """Estimate tokens for audio models based on duration and speech rate."""
        # Average speech rate: ~150 words per minute, ~1.3 tokens per word
        words_per_minute = 150
        tokens_per_word = 1.3
        
        duration_minutes = audio_duration_sec / 60
        estimated_tokens = int(duration_minutes * words_per_minute * tokens_per_word)
        
        # Calculate cost (Whisper pricing is per minute)
        pricing = self.get_model_pricing(model, "openai")
        cost_per_minute = pricing.get("audio", 0.006)
        cost_usd = duration_minutes * cost_per_minute
        
        return {
            "tokens": estimated_tokens,
            "cost_usd": cost_usd,
            "method": "audio_duration_estimation"
        }
    
    def process_conversation_tokens(self, conversation: List[Dict], llm_calls: List[Dict]) -> Dict[str, Any]:
        """
        Process conversation and LLM calls to calculate token usage and costs.
        
        Args:
            conversation: List of conversation turns
            llm_calls: List of LLM call metadata
            
        Returns:
            Dict containing aggregated token metrics and detailed breakdown
        """
        total_tokens_in = 0
        total_tokens_out = 0
        total_estimated_cost_usd = 0.0
        token_details = []
        
        # Create a mapping of LLM calls for efficient lookup
        calls_by_timestamp = {call.get("timestamp", ""): call for call in llm_calls}
        
        # Process each conversation turn
        for i, turn in enumerate(conversation):
            role = turn.get("role", "unknown")
            text = turn.get("text", "")
            
            # Find associated LLM calls for this turn
            turn_calls = [call for call in llm_calls if call.get("turn_index") == i]
            if not turn_calls and role == "ai":
                # Fallback: use recent calls if turn-specific data not available
                turn_calls = llm_calls[-2:] if llm_calls else []
            
            # Process token data for this turn
            turn_tokens_in = 0
            turn_tokens_out = 0
            turn_cost = 0.0
            
            for llm_call in turn_calls:
                model = llm_call.get("model", "unknown")
                provider = llm_call.get("provider", "openai")
                input_text = llm_call.get("input_text", "")
                output_text = llm_call.get("output_text", "")
                
                # Count input tokens
                if input_text:
                    input_result = self.count_tokens_by_provider(input_text, model, provider, is_input=True)
                    turn_tokens_in += input_result.get("tokens", 0)
                    turn_cost += input_result.get("cost_usd", 0.0)
                
                # Count output tokens
                if output_text:
                    output_result = self.count_tokens_by_provider(output_text, model, provider, is_input=False)
                    turn_tokens_out += output_result.get("tokens", 0)
                    turn_cost += output_result.get("cost_usd", 0.0)
                
                # Store detailed token info
                token_details.append({
                    "turn": i,
                    "role": role,
                    "model": model,
                    "provider": provider,
                    "input_tokens": input_result.get("tokens", 0) if input_text else 0,
                    "output_tokens": output_result.get("tokens", 0) if output_text else 0,
                    "cost_usd": turn_cost,
                    "method": input_result.get("method", "unknown") if input_text else output_result.get("method", "unknown")
                })
            
            # Aggregate session totals
            total_tokens_in += turn_tokens_in
            total_tokens_out += turn_tokens_out
            total_estimated_cost_usd += turn_cost
        
        return {
            "total_tokens_in": total_tokens_in,
            "total_tokens_out": total_tokens_out,
            "total_estimated_cost_usd": total_estimated_cost_usd,
            "token_details": token_details
        }
    
    def add_pricing_for_provider(self, provider: str, model_pricing: Dict[str, Dict[str, float]]):
        """
        Add pricing information for a new provider.
        
        Args:
            provider: Provider name (e.g., "anthropic", "cohere")
            model_pricing: Dict of model names to pricing info
        """
        self.pricing_table[provider.lower()] = model_pricing
        
        self.logger.info(
            f"Added pricing for provider {provider}",
            action="add_pricing_for_provider",
            input_data={"provider": provider, "models": list(model_pricing.keys())},
            layer="token_tracker"
        )
