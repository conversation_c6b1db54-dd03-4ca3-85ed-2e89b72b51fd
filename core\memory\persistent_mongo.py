# persistent_store.py
"""
Async MongoDB storage functions for persistent memory collections: users, call_sessions, pipelines, intent_history, and dialog logs.
"""
from core.memory.mongo_client import get_mongo_client
from core.memory.persistent_mongo_schema import DialogLog, User, CallSession, Pipeline, IntentHistory, PersonaUsageAnalytics
import os

async def save_dialog_to_mongo(session_id, user_id, duration, score, agent_performance):
    db = await get_mongo_client()
    collection = db["dialog"]
    doc = DialogLog(
        session_id=session_id,
        user_id=user_id,
        duration=duration,
        score=score,
        agent_performance=agent_performance or ""
    ).dict(exclude_unset=True)
    result = await collection.insert_one(doc)
    return str(result.inserted_id)

async def save_user_to_mongo(user_id, tags=None, voice_profile=None, preferred_language=None, default_intent=None, feedback_score_avg=None, schema_version=None):
    """
    Save or update user profile in MongoDB with automatic timestamp and call count management.
    Uses upsert to either create a new user or update existing user data.
    """
    from datetime import datetime, timezone
    db = await get_mongo_client()
    collection = db["users"]

    # Build and validate with Pydantic
    user = User(
        user_id=user_id,
        tags=tags,
        voice_profile=voice_profile,
        preferred_language=preferred_language,
        default_intent=default_intent,
        feedback_score_avg=feedback_score_avg,
        schema_version=schema_version
    )
    user_doc = user.dict(exclude_unset=True)

    # Prepare update document
    update_doc = {
        "$set": {
            **user_doc,
            "updated_at": datetime.now(timezone.utc)
        },
        "$setOnInsert": {
            "user_id": user_id,
            "created_at": datetime.now(timezone.utc),
            "call_count": 0
        },
        "$inc": {
            "call_count": 1  # Increment call count on each save
        }
    }

    # Use upsert to create or update
    result = await collection.update_one(
        {"user_id": user_id},
        update_doc,
        upsert=True
    )

    if result.upserted_id:
        return str(result.upserted_id)
    else:
        # For updates, return the user_id since no new document was created
        return user_id

async def save_call_session_to_mongo(session_id, user_id, timestamp, agent_profile_used=None, pipeline_id=None, states_visited=None, interrupts=None, outcome=None, final_action=None, call_score=None, duration_sec=None, latency_avg_ms=None, tts_characters_used=None, tokens_in=None, tokens_out=None, estimated_cost_usd=None, schema_version=None):
    db = await get_mongo_client()
    collection = db["call_sessions"]
    call_session = CallSession(
        session_id=session_id,
        user_id=user_id,
        timestamp=timestamp,
        agent_profile_used=agent_profile_used,
        pipeline_id=pipeline_id,
        states_visited=states_visited,
        interrupts=interrupts,
        outcome=outcome,
        final_action=final_action,
        call_score=call_score,
        duration_sec=duration_sec,
        latency_avg_ms=latency_avg_ms,
        tts_characters_used=tts_characters_used,
        tokens_in=tokens_in,
        tokens_out=tokens_out,
        estimated_cost_usd=estimated_cost_usd,
        schema_version=schema_version
    )
    doc = call_session.dict(exclude_unset=True)
    result = await collection.insert_one(doc)
    return str(result.inserted_id)

async def save_pipeline_to_mongo(pipeline_id, user_id=None, workflow_states=None, metadata=None, version=None, schema_version=None):
    db = await get_mongo_client()
    collection = db["pipelines"]
    pipeline = Pipeline(
        pipeline_id=pipeline_id,
        user_id=user_id,
        workflow_states=workflow_states,
        metadata=metadata,
        version=version,
        schema_version=schema_version
    )
    doc = pipeline.dict(exclude_unset=True)
    result = await collection.insert_one(doc)
    return str(result.inserted_id)

async def save_intent_history_to_mongo(user_id, session_id, raw_input, intent_detected, state_id, success, context_snapshot=None, schema_version=None):
    db = await get_mongo_client()
    collection = db["intent_history"]
    intent_history = IntentHistory(
        user_id=user_id,
        session_id=session_id,
        raw_input=raw_input,
        intent_detected=intent_detected,
        state_id=state_id,
        success=success,
        context_snapshot=context_snapshot,
        schema_version=schema_version
    )
    doc = intent_history.dict(exclude_unset=True)
    result = await collection.insert_one(doc)
    return str(result.inserted_id)

async def save_persona_usage_analytics_to_mongo(voice_id, persona_id, feedback_score=None, schema_version=None):
    """
    Save or update Persona Usage Analytics statistics in MongoDB.
    Increments call_count and updates avg_feedback_score for the (voice_id, persona_id) pair.
    """
    from datetime import datetime, timezone
    db = await get_mongo_client()
    collection = db["tts_usage"]

    # Fetch current stats
    existing = await collection.find_one({"voice_id": voice_id, "persona_id": persona_id})
    if existing:
        call_count = existing.get("call_count", 0) + 1
        prev_avg = existing.get("avg_feedback_score", 0.0)
        prev_total = prev_avg * existing.get("call_count", 0)
        if feedback_score is not None:
            new_avg = (prev_total + feedback_score) / call_count
        else:
            new_avg = prev_avg
    else:
        call_count = 1
        new_avg = feedback_score if feedback_score is not None else 0.0

    persona_usage = PersonaUsageAnalytics(
        voice_id=voice_id,
        persona_id=persona_id,
        call_count=call_count,
        avg_feedback_score=new_avg,
        schema_version=schema_version
    )
    persona_doc = persona_usage.dict(exclude_unset=True)

    update_doc = {
        "$set": {
            **persona_doc,
            "updated_at": datetime.now(timezone.utc)
        },
        "$setOnInsert": {
            "voice_id": voice_id,
            "persona_id": persona_id,
            "created_at": datetime.now(timezone.utc)
        },
        "$inc": {"call_count": 1}
    }

    result = await collection.update_one(
        {"voice_id": voice_id, "persona_id": persona_id},
        update_doc,
        upsert=True
    )
    if result.upserted_id:
        return str(result.upserted_id)
    else:
        return (voice_id, persona_id) 