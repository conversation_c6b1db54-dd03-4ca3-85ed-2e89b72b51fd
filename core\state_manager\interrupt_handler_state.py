"""
Interrupt Handler State for managing TTS interruptions.

This state handles the interrupt flow: detect → pause → acknowledge → resume
"""

import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from core.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from .pipeline_states_v2 import AbstractPipelineState
from utils.audio_utils import TTSPlaybackController
from core.interruption.action_reversibility import ActionReversibilityDetector

logger = get_module_logger("InterruptHandlerState")


class InterruptInputSchema(BaseModel):
    """Input schema for interrupt handler state."""
    interrupt_detected: bool = Field(description="Whether an interrupt was detected")
    user_input: Optional[str] = Field(None, description="User input that caused the interrupt")
    playback_position: Optional[float] = Field(None, description="Current playback position in seconds")
    audio_path: Optional[str] = Field(None, description="Path to the interrupted audio file")
    session_id: str = Field(description="Session ID")


class InterruptOutputSchema(BaseModel):
    """Output schema for interrupt handler state."""
    interrupt_handled: bool = Field(description="Whether the interrupt was successfully handled")
    acknowledgment_message: str = Field(description="Message acknowledging the interrupt")
    should_resume_tts: bool = Field(description="Whether TTS should be resumed")
    queued_user_input: Optional[str] = Field(None, description="User input queued for processing")
    action_reversible: bool = Field(True, description="Whether the interrupted action is reversible")


class InterruptHandlerState(AbstractPipelineState):
    """
    Interrupt Handler State
    Manages the interrupt flow with grace period, polite acknowledgment, and resume functionality.
    """
    input_schema_class = InterruptInputSchema
    output_schema_class = InterruptOutputSchema

    def __init__(self, state_id: str, agent_registry, session_id: str, interrupt_config=None):
        super().__init__(state_id, agent_registry, session_id)
        self.interrupt_config = interrupt_config
        if interrupt_config is not None:
            self.confirmation_window = interrupt_config.detection.confirmation_window_seconds
        else:
            self.confirmation_window = 0.5  # fallback default
        self.reversibility_detector = ActionReversibilityDetector()

    async def process(self, input_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> StateOutput:
        """
        Process interrupt handling with grace period and acknowledgment.
        
        Flow:
        1. Confirm interrupt (grace period)
        2. Determine action reversibility
        3. Generate appropriate acknowledgment
        4. Store interrupt context
        5. Return handling instructions
        """
        if self.interrupt_config and not self.interrupt_config.detection.enabled:
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Interrupt detection is disabled in config.",
                code=StatusCode.OK,
                outputs={"interrupt_handled": False, "detection_disabled": True},
                meta={"interrupt_detection_enabled": False}
            )
        try:
            validated_input = self.validate_input(input_data)
        except Exception as e:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"InterruptHandlerState input validation error: {str(e)}",
                code=StatusCode.VALIDATION_ERROR,
                outputs={},
                meta={"agent": self.id, "validation_error": str(e)}
            )

        try:
            session_id = validated_input.session_id
            
            self.logger.info(
                "Processing interrupt handling",
                action="process_interrupt",
                input_data=validated_input.model_dump(),
                layer="interrupt_handler_state"
            )

            # Step 1: Confirm interrupt with grace period
            interrupt_confirmed = await self._confirm_interrupt(validated_input)
            
            if not interrupt_confirmed:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="Interrupt not confirmed, continuing normal flow",
                    code=StatusCode.OK,
                    outputs={
                        "interrupt_handled": False,
                        "acknowledgment_message": "",
                        "should_resume_tts": True,
                        "queued_user_input": None,
                        "action_reversible": True
                    },
                    meta={"interrupt_confirmed": False}
                )

            # Step 2: Determine action reversibility using the detector
            action_metadata = self.reversibility_detector.get_action_metadata(context or {})
            action_reversible = action_metadata.reversibility.value != "irreversible"

            # Step 3: Generate appropriate acknowledgment
            acknowledgment = action_metadata.interrupt_message
            
            # Step 4: Store interrupt context in memory
            await self._store_interrupt_context(validated_input, action_reversible, acknowledgment)
            
            # Step 5: Log interrupt event
            await self._log_interrupt_event(validated_input, acknowledgment)
            
            # Step 6: Prepare output
            result_outputs = {
                "interrupt_handled": True,
                "acknowledgment_message": acknowledgment,
                "should_resume_tts": not action_reversible,  # Don't resume if action is irreversible
                "queued_user_input": validated_input.user_input,
                "action_reversible": action_reversible,
                "orchestrator_note": "After acknowledgment, resume TTS playback, then process queued user input."
            }

            await self._publish_notification("complete", result_outputs, list(result_outputs.keys()))

            return StateOutput(
                status=StatusType.SUCCESS,
                message="Interrupt handled successfully",
                code=StatusCode.OK,
                outputs=result_outputs,
                meta={"interrupt_confirmed": True, "action_reversible": action_reversible}
            )

        except Exception as e:
            await self._publish_notification("error", {}, error_message=str(e))
            return StateOutput(
                status=StatusType.ERROR,
                message=f"InterruptHandlerState error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.id, "error": str(e)}
            )

    async def _confirm_interrupt(self, input_data: InterruptInputSchema) -> bool:
        """
        Confirm interrupt with grace period to avoid false positives.
        
        Args:
            input_data: Validated input data
            
        Returns:
            bool: True if interrupt is confirmed, False otherwise
        """
        try:
            if not input_data.interrupt_detected:
                return False
            
            # Wait for confirmation window
            await asyncio.sleep(self.confirmation_window)
            
            # In a real implementation, this would re-check VAD
            # For now, we'll assume the interrupt is confirmed if initially detected
            confirmed = input_data.interrupt_detected and input_data.user_input is not None
            
            self.logger.info(
                "Interrupt confirmation completed",
                action="_confirm_interrupt",
                output_data={"confirmed": confirmed, "confirmation_window": self.confirmation_window},
                layer="interrupt_handler_state"
            )
            
            return confirmed
            
        except Exception as e:
            self.logger.error(
                "Error confirming interrupt",
                action="_confirm_interrupt",
                reason=str(e),
                layer="interrupt_handler_state"
            )
            return False

    # Note: Action reversibility detection is now handled by ActionReversibilityDetector

    async def _store_interrupt_context(self, input_data: InterruptInputSchema, 
                                     action_reversible: bool, acknowledgment: str):
        """Store interrupt context in memory for later processing."""
        try:
            # Get memory manager from agent registry (if available)
            # This is a simplified approach - in production you'd have better access to memory
            context_data = {
                "detected": True,
                "confirmed": True,
                "user_input_queued": input_data.user_input,
                "resume_after_acknowledgment": action_reversible,
                "action_reversible": action_reversible,
                "acknowledgment_message": acknowledgment,
                "playback_position": input_data.playback_position,
                "audio_path": input_data.audio_path,
                "interrupt_timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(
                "Interrupt context stored",
                action="_store_interrupt_context",
                output_data=context_data,
                layer="interrupt_handler_state"
            )
            
        except Exception as e:
            self.logger.error(
                "Error storing interrupt context",
                action="_store_interrupt_context",
                reason=str(e),
                layer="interrupt_handler_state"
            )

    async def _log_interrupt_event(self, input_data: InterruptInputSchema, acknowledgment: str):
        """Log interrupt event for analytics and debugging."""
        try:
            event_data = {
                "event_type": "interrupt_handled",
                "session_id": input_data.session_id,
                "user_input": input_data.user_input,
                "playback_position": input_data.playback_position,
                "acknowledgment": acknowledgment,
                "timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(
                "Interrupt event logged",
                action="interrupt_event",
                output_data=event_data,
                layer="interrupt_handler_state"
            )
            
        except Exception as e:
            self.logger.error(
                "Error logging interrupt event",
                action="_log_interrupt_event",
                reason=str(e),
                layer="interrupt_handler_state"
            )
