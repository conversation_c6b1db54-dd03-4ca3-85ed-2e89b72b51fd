from agents.base.base_agent import AudioAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from utils.audio_utils import synthesize_fallback_audio
from utils.llm_call_tracker import track_audio_call

import os
import openai
import asyncio

class STTAgent(AudioAgent):
    def __init__(self, session_id=None, state_id=None):
        super().__init__(session_id, state_id)
        self.agent_name = "stt_agent"
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        self.client = openai.OpenAI(api_key=openai_api_key)

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def speech_to_text(self, audio_data: bytes, session_context=None) -> StateOutput:
        start_time = asyncio.get_event_loop().time()
        session_context = session_context or {}
        self._log_process_start(
            {"type": str(type(audio_data)), "size": len(audio_data) if isinstance(audio_data, bytes) else None},
            session_context
        )
        audio_path = None
        try:
            language = session_context.get("language", None)
            response_format = session_context.get("response_format", "text")
            session_id = self.session_id or session_context.get("session_id")
            # Save bytes to a temp file if needed
            if isinstance(audio_data, str) and os.path.isfile(audio_data):
                audio_path = audio_data
            elif isinstance(audio_data, bytes):
                import tempfile
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp:
                    tmp.write(audio_data)
                    tmp.flush()
                    audio_path = tmp.name
            else:
                raise ValueError("audio_data must be a file path or bytes buffer")

            def sync_transcribe():
                with open(audio_path, "rb") as audio_file:
                    return self.client.audio.transcriptions.create(
                        model="whisper-1",
                        file=audio_file,
                        language=language,
                        response_format=response_format
                    )

            transcription = await asyncio.to_thread(sync_transcribe)
            if hasattr(transcription, "text"):
                text = transcription.text
            else:
                text = transcription

            # Estimate audio duration (simplified - in production use proper audio library)
            try:
                import os
                file_size = os.path.getsize(audio_path)
                # Rough estimation: ~1KB per second for compressed audio
                audio_duration_sec = max(1.0, file_size / 1024)
            except:
                audio_duration_sec = 10.0  # Default fallback

            # Track LLM call for token usage
            await track_audio_call(self.memory_manager, "whisper-1", audio_duration_sec, text, "stt")

            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            result = StateOutput(
                status=StatusType.SUCCESS,
                message="STT processed successfully",
                code=StatusCode.OK,
                outputs={"text": text, "latencySTT": duration_ms},
                meta={
                    "agent": self.agent_name,
                    "latencySTT": duration_ms
                }
            )
            self._log_process_end(result, duration_ms)

            # Save transcript and latency to Redis and publish notification
            if session_id:
                # Always reload the latest context before saving
                shared_context = await self.load_context(session_id) or {}
                fallback_result = await self.handle_redis_fallback(shared_context, session_id)
                if fallback_result:
                    return fallback_result
                shared_context["transcript"] = text
                shared_context["latencySTT"] = duration_ms
                await self.save_context(session_id, shared_context)
                notification = A2AMessage(
                    session_id=session_id,
                    message_type=MessageType.NOTIFICATION,
                    source_agent=self.agent_name,
                    target_agent="Orchestrator",
                    payload={"status": "complete"},
                    context_keys_updated=["transcript", "latencySTT"]
                )
                # await self.publish_notification("agent_completion", notification.to_json())
            return result
        except Exception as e:
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            self._log_error(e, {"type": str(type(audio_data)), "size": len(audio_data) if isinstance(audio_data, bytes) else None})
            # Notify orchestrator of error
            if self.session_id or (session_context and session_context.get("session_id")):
                notification = A2AMessage(
                    session_id=self.session_id or session_context.get("session_id"),
                    message_type=MessageType.NOTIFICATION,
                    source_agent=self.agent_name,
                    target_agent="Orchestrator",
                    payload={"status": "error", "error_message": str(e)},
                    context_keys_updated=[]
                )
                # await self.publish_notification("agent_completion", notification.to_json())
            # Generate fallback audio
            fallback_text = "Sorry, something went wrong. Can you repeat that please?"
            audio_path = synthesize_fallback_audio(fallback_text, session_id=self.session_id or "fallback")
            outputs = {"fallback_message": fallback_text}
            if audio_path:
                outputs["audio_path"] = audio_path
            return StateOutput(
                status=StatusType.ERROR,
                message=f"STTAgent error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencySTT": duration_ms,
                    "error": str(e)
                }
            )
        finally:
            if audio_path and (isinstance(audio_data, bytes)) and os.path.exists(audio_path):
                try:
                    os.remove(audio_path)
                except Exception as cleanup_err:
                    self.logger.warning(f"Could not remove temp file: {audio_path}. Error: {cleanup_err}")

    async def process(self, input_data, session_context=None):
        if not isinstance(input_data, (bytes, str, dict)):
            return StateOutput(
                status=StatusType.ERROR,
                message="Input must be bytes or a file path",
                code=StatusCode.BAD_REQUEST,
                outputs={}
            )
        if isinstance(input_data, dict):
            if "audio_path" in input_data:
                input_data = input_data["audio_path"]
            else:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Input dictionary must contain 'audio_path' or 'audio_data'",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"agent": self.agent_name}
                )
        return await self.speech_to_text(input_data, session_context)
        
    #TODO IMPLEMENT MCP MESSAGE
    # async def handle_mcp_message(self, message):

    #         return StateOutput(
    #             status=StatusType.ERROR,
    #             message=f"handle_mcp_message error: {str(e)}",
    #             code=StatusCode.INTERNAL_ERROR,
    #             outputs={},
    #             meta={"agent": self.agent_name, "error": str(e)}
    #         )

